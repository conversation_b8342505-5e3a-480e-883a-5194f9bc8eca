using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using HandyControl.Data;
using System.Collections.ObjectModel;
using System.Drawing;
using HandyControl.Controls;
using GaugeCtrl.Communication;
using GaugeCtrl.Core.Models;
using System.Reflection;
using System.Windows;
using MessageBox = HandyControl.Controls.MessageBox;
using GaugeCtrl.Helpers;
using Microsoft.Win32;
using System.IO;
using System.Text.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using GaugeCtrl.Communication.Interfaces;

namespace GaugeCtrl.ViewModels
{
    public partial class MainWindowViewModel : ObservableObject
    {
        [ObservableProperty] private object _currentView;

        [ObservableProperty] private string _selectedPage = "ConnectDevice";

        [ObservableProperty] private byte _currentSlaveId = 1;

        /// <summary>
        /// 子节点启用状态
        /// </summary>
        [ObservableProperty] private bool _isMasterNodeEnabled = false;

        /// <summary>
        /// 当前选择的轴
        /// </summary>
        [ObservableProperty] private AxisType _selectedAxis = AxisType.Axis0;

        /// <summary>
        /// 是否需要重新读取参数
        /// </summary>
        [ObservableProperty] private bool _needReloadParameters = false;

        /// <summary>
        /// 是否有参数被修改
        /// </summary>
        [ObservableProperty] private bool _hasModifiedParameters = false;

        public string WindowTitle => $"BLDC Tools-v{GetApplicationVersion()}";

        /// <summary>
        /// 从站地址选项
        /// </summary>
        public ObservableCollection<byte> SlaveAddressOptions { get; } = new ObservableCollection<byte>();

        /// <summary>
        /// 轴选择选项
        /// </summary>
        public ObservableCollection<AxisType> AxisOptions { get; } = new ObservableCollection<AxisType>
            { AxisType.Axis0, AxisType.Axis1 };

        /// <summary>
        /// 搜索到的从站地址（用于子节点模式）
        /// </summary>
        public ObservableCollection<byte> FoundSlaveAddresses { get; } = new ObservableCollection<byte>();

        private readonly SerialSettingsViewModel _serialSettingsViewModel;
        private readonly OscilloscopeViewModel _oscilloscopeViewModel;
        private readonly ParameterSettingsViewModel _parameterSettingsViewModel;
        private readonly MotionSimulationViewModel _motionSimulationViewModel;
        private readonly FirmwareUpgradeViewModel _firmwareUpgradeViewModel;
        private readonly ISerialPortManager _serialPortManager;

        public SerialSettingsViewModel SerialSettingsViewModel => _serialSettingsViewModel;
        public OscilloscopeViewModel OscilloscopeViewModel => _oscilloscopeViewModel;
        public ParameterSettingsViewModel ParameterSettingsViewModel => _parameterSettingsViewModel;
        public MotionSimulationViewModel MotionSimulationViewModel => _motionSimulationViewModel;
        public FirmwareUpgradeViewModel FirmwareUpgradeViewModel => _firmwareUpgradeViewModel;
        public ISerialPortManager SerialPortManager => _serialPortManager;

        public MainWindowViewModel()
        {
            // 使用单例串口管理器，确保全局统一
            _serialPortManager = SerialPortManagerSingleton.Instance;
            _serialSettingsViewModel = new SerialSettingsViewModel(_serialPortManager);
            _oscilloscopeViewModel = new OscilloscopeViewModel(_serialPortManager);
            _parameterSettingsViewModel = new ParameterSettingsViewModel(_serialPortManager);
            _motionSimulationViewModel = new MotionSimulationViewModel(_serialPortManager);
            _firmwareUpgradeViewModel = new FirmwareUpgradeViewModel();


            // 订阅串口设置视图模型的搜索完成事件
            _serialSettingsViewModel.SlaveSearchCompleted += OnSlaveSearchCompleted;

            // 订阅串口设置视图模型的主节点状态变更事件
            _serialSettingsViewModel.MasterNodeEnabledChanged += OnSerialSettingsMasterNodeEnabledChanged;

            // 订阅握手完成事件，用于自动下载参数
            _serialSettingsViewModel.HandshakeCompleted += OnHandshakeCompleted;

            // 订阅参数修改状态变更事件
            _parameterSettingsViewModel.ParameterModificationChanged += OnParameterModificationChanged;

            // 设置子节点状态获取器
            _serialSettingsViewModel.SetMasterNodeEnabledGetter(() => IsMasterNodeEnabled);

            // 默认显示连接设备页面
            CurrentView = _serialSettingsViewModel;
        }

        partial void OnSelectedAxisChanged(AxisType value)
        {
            // 将轴选择传递给参数设置ViewModel
            _parameterSettingsViewModel.SelectedAxis = value;

            // 提示需要重新读取参数
            NeedReloadParameters = true;
            MessageHelper.Info("轴选择已更改，建议重新下载参数以获取最新数据。");
        }

        // 简化的页面切换Command
        [RelayCommand]
        private void SwitchView(string tag)
        {
            // 如果当前在参数设置页面且有参数被修改，提示保存
            if (SelectedPage == "ParameterSettings" && tag != "ParameterSettings" && HasModifiedParameters)
            {
                var result = MessageBox.Show("检测到参数已修改但未上传，是否先上传参数？", "确认操作",
                    System.Windows.MessageBoxButton.YesNoCancel, System.Windows.MessageBoxImage.Question);

                if (result == System.Windows.MessageBoxResult.Yes)
                {
                    // 用户选择上传参数
                    _ = Task.Run(async () =>
                    {
                        await UploadParameters();
                        // 上传完成后切换页面
                        Application.Current.Dispatcher.Invoke(() => { PerformPageSwitch(tag); });
                    });
                    return;
                }
                else if (result == System.Windows.MessageBoxResult.Cancel)
                {
                    // 用户取消操作，不切换页面
                    return;
                }
                // 用户选择不保存，继续切换页面
            }

            PerformPageSwitch(tag);
        }

        /// <summary>
        /// 执行页面切换
        /// </summary>
        private void PerformPageSwitch(string tag)
        {
            SelectedPage = tag;
            // 根据Tag参数切换页面
            CurrentView = tag switch
            {
                "ConnectDevice" => _serialSettingsViewModel,
                "ParameterSettings" => _parameterSettingsViewModel,
                "MotionSimulation" => _motionSimulationViewModel,
                "StatusMonitoring" => _oscilloscopeViewModel,
                "FirmwareUpgrade" => _firmwareUpgradeViewModel,
                _ => _serialSettingsViewModel,
            };
        }

        // 文件操作Commands
        [RelayCommand]
        private void ExportParameters()
        {
            // TODO: 实现导出参数文件功能
            Growl.Success("参数文件导出成功！");
        }

        [RelayCommand]
        private void ImportParameters()
        {
            try
            {
                // 创建文件选择对话框
                var openFileDialog = new OpenFileDialog
                {
                    Title = "选择参数文件",
                    Filter = "JSON文件 (*.json)|*.json|所有文件 (*.*)|*.*",
                    FilterIndex = 1,
                    RestoreDirectory = true
                };

                // 显示对话框
                if (openFileDialog.ShowDialog() == true)
                {
                    var filePath = openFileDialog.FileName;

                    // 验证文件是否存在
                    if (!File.Exists(filePath))
                    {
                        MessageHelper.Error("选择的文件不存在！");
                        return;
                    }

                    // 读取并解析JSON文件
                    var jsonContent = File.ReadAllText(filePath);

                    if (string.IsNullOrWhiteSpace(jsonContent))
                    {
                        MessageHelper.Error("参数文件内容为空！");
                        return;
                    }

                    // 解析JSON数据
                    var parameterData = JsonSerializer.Deserialize<Dictionary<string, object>>(jsonContent);

                    if (parameterData == null || parameterData.Count == 0)
                    {
                        MessageHelper.Error("参数文件格式不正确或内容为空！");
                        return;
                    }

                    // 确认导入操作
                    MessageHelper.Ask(
                        $"确定要导入参数文件 '{Path.GetFileName(filePath)}' 吗？\n\n此操作将覆盖当前的参数设置。",
                        "确认导入",
                        onConfirm: () =>
                        {
                            try
                            {
                                // 执行参数导入
                                ImportParametersFromData(parameterData);
                                MessageHelper.Success($"参数文件 '{Path.GetFileName(filePath)}' 导入成功！");
                            }
                            catch (Exception ex)
                            {
                                MessageHelper.Error("导入参数时发生错误", ex);
                            }
                        },
                        onCancel: () => { MessageHelper.Info("已取消参数导入操作。"); }
                    );
                }
            }
            catch (Exception ex)
            {
                MessageHelper.Error("选择参数文件时发生错误", ex);
            }
        }

        /// <summary>
        /// 从数据字典导入参数到ParameterSettingsViewModel
        /// </summary>
        /// <param name="parameterData">参数数据字典</param>
        private void ImportParametersFromData(Dictionary<string, object> parameterData)
        {
            var parameterSettings = _parameterSettingsViewModel;
            int importedCount = 0;
            int errorCount = 0;
            var errorMessages = new List<string>();

            try
            {
                // 导入IO参数
                if (parameterData.ContainsKey("IOParameters"))
                {
                    var result = ImportParameterCollection(
                        parameterData["IOParameters"],
                        parameterSettings.IoParameters.Cast<object>().ToList(),
                        "IO参数"
                    );
                    importedCount += result.ImportedCount;
                    errorCount += result.ErrorCount;
                    errorMessages.AddRange(result.ErrorMessages);
                }

                // 导入保护参数
                if (parameterData.ContainsKey("ProtectionParameters"))
                {
                    var result = ImportParameterCollection(
                        parameterData["ProtectionParameters"],
                        parameterSettings.ProtectionParameters.Cast<object>().ToList(),
                        "保护参数"
                    );
                    importedCount += result.ImportedCount;
                    errorCount += result.ErrorCount;
                    errorMessages.AddRange(result.ErrorMessages);
                }

                // 导入控制参数
                if (parameterData.ContainsKey("ControlParameters"))
                {
                    var result = ImportParameterCollection(
                        parameterData["ControlParameters"],
                        parameterSettings.ControlParameters.Cast<object>().ToList(),
                        "控制参数"
                    );
                    importedCount += result.ImportedCount;
                    errorCount += result.ErrorCount;
                    errorMessages.AddRange(result.ErrorMessages);
                }

                // 导入运动参数
                if (parameterData.ContainsKey("MotionParameters"))
                {
                    var result = ImportParameterCollection(
                        parameterData["MotionParameters"],
                        parameterSettings.MotionParameters.Cast<object>().ToList(),
                        "运动参数"
                    );
                    importedCount += result.ImportedCount;
                    errorCount += result.ErrorCount;
                    errorMessages.AddRange(result.ErrorMessages);
                }

                // 导入监控参数
                if (parameterData.ContainsKey("MonitoringParameters"))
                {
                    var result = ImportParameterCollection(
                        parameterData["MonitoringParameters"],
                        parameterSettings.MonitoringParameters.Cast<object>().ToList(),
                        "监控参数"
                    );
                    importedCount += result.ImportedCount;
                    errorCount += result.ErrorCount;
                    errorMessages.AddRange(result.ErrorMessages);
                }

                // 显示导入结果
                if (errorCount > 0)
                {
                    var errorSummary = string.Join("\n", errorMessages.Take(5)); // 只显示前5个错误
                    if (errorMessages.Count > 5)
                    {
                        errorSummary += $"\n... 还有 {errorMessages.Count - 5} 个错误";
                    }

                    MessageHelper.Warning(
                        $"参数导入完成，但存在问题：\n" +
                        $"成功导入: {importedCount} 个参数\n" +
                        $"导入失败: {errorCount} 个参数\n\n" +
                        $"错误详情：\n{errorSummary}"
                    );
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"导入参数数据时发生错误: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 导入参数集合的结果
        /// </summary>
        private class ImportResult
        {
            public int ImportedCount { get; set; }
            public int ErrorCount { get; set; }
            public List<string> ErrorMessages { get; set; } = new List<string>();
        }

        /// <summary>
        /// 导入单个参数集合
        /// </summary>
        /// <param name="sourceData">源数据</param>
        /// <param name="targetParameters">目标参数集合</param>
        /// <param name="parameterTypeName">参数类型名称</param>
        /// <returns>导入结果</returns>
        private ImportResult ImportParameterCollection(object sourceData, List<object> targetParameters,
            string parameterTypeName)
        {
            var result = new ImportResult();

            try
            {
                if (sourceData is JsonElement jsonElement && jsonElement.ValueKind == JsonValueKind.Array)
                {
                    foreach (var item in jsonElement.EnumerateArray())
                    {
                        try
                        {
                            if (item.TryGetProperty("Name", out var nameProperty) &&
                                item.TryGetProperty("Value", out var valueProperty))
                            {
                                var parameterName = nameProperty.GetString();

                                // 查找对应的参数
                                var targetParameter = targetParameters.FirstOrDefault(p =>
                                {
                                    var nameProperty = p.GetType().GetProperty("Name");
                                    return nameProperty?.GetValue(p)?.ToString() == parameterName;
                                });

                                if (targetParameter != null)
                                {
                                    // 获取参数的Value属性
                                    var valuePropertyInfo = targetParameter.GetType().GetProperty("Value");
                                    if (valuePropertyInfo != null)
                                    {
                                        // 根据参数类型转换值
                                        var convertedValue =
                                            ConvertJsonValueToParameterValue(valueProperty, targetParameter);
                                        valuePropertyInfo.SetValue(targetParameter, convertedValue);
                                        result.ImportedCount++;
                                    }
                                }
                                else
                                {
                                    result.ErrorCount++;
                                    result.ErrorMessages.Add($"{parameterTypeName}: 未找到参数 '{parameterName}'");
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            result.ErrorCount++;
                            result.ErrorMessages.Add($"{parameterTypeName}: 导入参数时出错 - {ex.Message}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                result.ErrorCount++;
                result.ErrorMessages.Add($"{parameterTypeName}: 解析参数集合时出错 - {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// 将JSON值转换为参数值
        /// </summary>
        /// <param name="jsonValue">JSON值</param>
        /// <param name="targetParameter">目标参数</param>
        /// <returns>转换后的值</returns>
        private object ConvertJsonValueToParameterValue(JsonElement jsonValue, object targetParameter)
        {
            // 获取参数类型
            var parameterTypeProperty = targetParameter.GetType().GetProperty("ParameterType");
            if (parameterTypeProperty?.GetValue(targetParameter) is ParameterType parameterType)
            {
                switch (parameterType)
                {
                    case ParameterType.Boolean:
                        return jsonValue.ValueKind == JsonValueKind.True ||
                               (jsonValue.ValueKind == JsonValueKind.String &&
                                bool.TryParse(jsonValue.GetString(), out var boolResult) && boolResult);

                    case ParameterType.Numeric:
                        if (jsonValue.ValueKind == JsonValueKind.Number)
                        {
                            if (jsonValue.TryGetInt32(out var intValue))
                                return intValue;
                            if (jsonValue.TryGetDouble(out var doubleValue))
                                return doubleValue;
                        }
                        else if (jsonValue.ValueKind == JsonValueKind.String)
                        {
                            var stringValue = jsonValue.GetString();
                            if (int.TryParse(stringValue, out var intValue))
                                return intValue;
                            if (double.TryParse(stringValue, out var doubleValue))
                                return doubleValue;
                        }

                        break;

                    case ParameterType.Text:
                    case ParameterType.Enum:
                        return jsonValue.GetString() ?? string.Empty;
                }
            }

            // 默认返回字符串值
            return jsonValue.ValueKind == JsonValueKind.String ? jsonValue.GetString() : jsonValue.ToString();
        }

        [RelayCommand]
        private void ResetToFactory()
        {
            // TODO: 实现恢复出厂设置功能
            var result = MessageBox.Show("确定要恢复出厂设置吗？此操作将清除所有自定义参数。", "确认操作",
                System.Windows.MessageBoxButton.YesNo, System.Windows.MessageBoxImage.Warning);

            if (result == System.Windows.MessageBoxResult.Yes)
            {
                Growl.Success("已恢复出厂设置！");
            }
        }

        // 参数上传下载Commands
        [RelayCommand]
        private async Task UploadParameters()
        {
            try
            {
                var modbusHandler = _serialPortManager?.GetModbusHandler();
                if (modbusHandler == null)
                {
                    MessageHelper.Error("串口未连接，无法上传参数！");
                    return;
                }

                if (!modbusHandler.IsConnected)
                {
                    MessageHelper.Error("串口连接已断开，无法上传参数！");
                    return;
                }

                // 获取所有已修改的参数
                var modifiedParameters = GetModifiedParameters();
                if (modifiedParameters.Count == 0)
                {
                    MessageHelper.Info("没有参数被修改，无需上传。");
                    return;
                }

                // 确认上传操作
                var result = MessageBox.Show($"确定要上传 {modifiedParameters.Count} 个已修改的参数吗？", "确认上传",
                    System.Windows.MessageBoxButton.YesNo, System.Windows.MessageBoxImage.Question);

                if (result != System.Windows.MessageBoxResult.Yes)
                    return;

                // 循环写入已修改的参数
                int successCount = 0;
                int failCount = 0;

                foreach (var param in modifiedParameters)
                {
                    try
                    {
                        var value = param.Value;
                        var registerAddress = param.RegisterAddress;
                        var paramName = param.Name;

                        var ushortValue = ConvertParameterValueToUshort(value);
                        var success =
                            await modbusHandler.WriteSingleRegisterAsync(CurrentSlaveId, registerAddress, ushortValue);

                        if (success)
                        {
                            successCount++;
                            // 标记参数为未修改状态
                            param.IsModified = false;
                        }
                        else
                        {
                            failCount++;
                        }

                        // 添加小延时避免过快的请求
                        await Task.Delay(50);
                    }
                    catch (Exception ex)
                    {
                        failCount++;
                        var paramName = param.Name;
                        System.Diagnostics.Debug.WriteLine($"上传参数 {paramName} 失败: {ex.Message}");
                    }
                }

                if (failCount == 0)
                {
                    MessageHelper.Success($"成功上传 {successCount} 个参数！");
                    HasModifiedParameters = false; // 重置标志
                }
                else
                {
                    MessageHelper.Warning($"上传完成：成功 {successCount} 个，失败 {failCount} 个。");
                    // 重新检查修改状态
                    _parameterSettingsViewModel.CheckParameterModificationStatus();
                }
            }
            catch (Exception ex)
            {
                MessageHelper.Error($"上传参数时发生错误：{ex.Message}");
            }
        }


        [RelayCommand]
        private async Task DownloadParameters()
        {
            try
            {
                var modbusHandler = _serialPortManager?.GetModbusHandler();
                if (modbusHandler == null)
                {
                    MessageHelper.Error("串口未连接，无法下载参数！");
                    return;
                }

                if (!modbusHandler.IsConnected)
                {
                    MessageHelper.Error("串口连接已断开，无法下载参数！");
                    return;
                }

                // 获取所有有寄存器地址的参数
                var parametersWithRegister = GetParametersWithRegisterAddress();
                if (parametersWithRegister.Count == 0)
                {
                    MessageHelper.Info("没有可下载的参数。");
                    return;
                }

                // 循环读取单个寄存器
                int successCount = 0;
                int failCount = 0;

                foreach (var param in parametersWithRegister)
                {
                    try
                    {
                        var registerAddress = param.RegisterAddress;// GetParameterRegisterAddress(param);
                        var paramName = param.Name;// GetParameterName(param);

                        var value = await modbusHandler.ReadHoldingRegisterAsync(CurrentSlaveId, registerAddress);
                        // 添加小延时避免过快的请求
                        await Task.Delay(50);
                        if (value.HasValue)
                        {
                            param.Value = Convert.ToInt32(value);
                            param.OriginalValue = Convert.ToInt32(value);
                            param.IsModified = false;
                            successCount++;
                        }
                        else
                        {
                            failCount++;
                        }
                    }
                    catch (Exception ex)
                    {
                        failCount++;
                        var paramName = param.Name;
                        System.Diagnostics.Debug.WriteLine($"下载参数 {paramName} 失败: {ex.Message}");
                    }
                }

                if (failCount == 0)
                {
                    MessageHelper.Success($"成功下载 {successCount} 个参数！");
                    NeedReloadParameters = false; // 重置标志
                    // 重新检查修改状态，确保UI状态正确
                    _parameterSettingsViewModel.CheckParameterModificationStatus();
                }
                else
                {
                    MessageHelper.Warning($"下载完成：成功 {successCount} 个，失败 {failCount} 个。");
                }
            }
            catch (Exception ex)
            {
                MessageHelper.Error($"下载参数时发生错误：{ex.Message}");
            }
        }

        /// <summary>
        /// 获取所有已修改的参数
        /// </summary>
        private List<Parameter> GetModifiedParameters()
        {
            var modifiedParams = _parameterSettingsViewModel.MotorParameters.Where(param => param.IsModified).ToList();

            // 检查电机参数

            // 检查保护参数
            modifiedParams.AddRange(_parameterSettingsViewModel.ProtectionParameters.Where(param => param.IsModified));

            // 检查控制参数
            modifiedParams.AddRange(_parameterSettingsViewModel.ControlParameters.Where(param => param.IsModified));

            // 检查运动参数
            modifiedParams.AddRange(_parameterSettingsViewModel.MotionParameters.Where(param => param.IsModified));

            return modifiedParams;
        }

        /// <summary>
        /// 获取所有有寄存器地址的参数
        /// </summary>
        private List<Parameter> GetParametersWithRegisterAddress()
        {
            // 添加电机参数
            var parametersWithRegister = _parameterSettingsViewModel.MotorParameters.Cast<Parameter>().ToList();
            // 添加保护参数
            parametersWithRegister.AddRange(_parameterSettingsViewModel.ProtectionParameters.Cast<Parameter>());
            // 添加控制参数
            parametersWithRegister.AddRange(_parameterSettingsViewModel.ControlParameters.Cast<Parameter>());
            // 添加运动参数
            parametersWithRegister.AddRange(_parameterSettingsViewModel.MotionParameters.Cast<Parameter>());

            return parametersWithRegister;
        }

        /// <summary>
        /// 将参数值转换为ushort类型
        /// </summary>
        private ushort ConvertParameterValueToUshort(object value)
        {
            if (value == null) return 0;

            if (value is ushort ushortValue)
                return ushortValue;

            if (value is int intValue)
                return (ushort)Math.Max(0, Math.Min(65535, intValue));

            if (value is double doubleValue)
                return (ushort)Math.Max(0, Math.Min(65535, Math.Round(doubleValue)));

            if (value is string stringValue && ushort.TryParse(stringValue, out var parsedValue))
                return parsedValue;

            return 0;
        }

        /// <summary>
        /// 获取参数的值
        /// </summary>
        //private object GetParameterValue(object param)
        //{
        //    var valueProperty = param.GetType().GetProperty("Value");
        //    return valueProperty?.GetValue(param);
        //}


        /// <summary>
        /// 获取参数的寄存器地址
        /// </summary>
        //private ushort GetParameterRegisterAddress(object param)
        //{
        //    var registerProperty = param.GetType().GetProperty("RegisterAddress");
        //    var value = registerProperty?.GetValue(param);
        //    return value is ushort address ? address : (ushort)0;
        //}

        /// <summary>
        /// 获取参数的名称
        /// </summary>
        //private string GetParameterName(object param)
        //{
        //    var nameProperty = param.GetType().GetProperty("Name");
        //    return nameProperty?.GetValue(param)?.ToString() ?? "未知参数";
        //}

        /// <summary>
        /// 设置参数的修改状态
        /// </summary>
        //private void SetParameterModified(object param, bool isModified)
        //{
        //    var modifiedProperty = param.GetType().GetProperty("IsModified");
        //    modifiedProperty?.SetValue(param, isModified);
        //}

        private string GetApplicationVersion()
        {
            var assembly = Assembly.GetExecutingAssembly();
            var version = assembly.GetName().Version;
            return version?.ToString(3) ?? "1.0.0"; // 返回主版本.次版本.修订版本
        }

        /// <summary>
        /// 当前从站地址变更时的处理
        /// </summary>
        partial void OnCurrentSlaveIdChanged(byte value)
        {
            // 同步到 ModbusProtocolHandler
            var modbusHandler = _serialPortManager?.GetModbusHandler();
            if (modbusHandler != null)
            {
                modbusHandler.CurrentSlaveId = value;
            }

            // 提示需要重新读取参数
            NeedReloadParameters = true;
            MessageHelper.Info("从站地址已更改，建议重新下载参数以获取最新数据。");
        }

        /// <summary>
        /// 主节点启用状态变更时的处理
        /// </summary>
        partial void OnIsMasterNodeEnabledChanged(bool value)
        {
            // 先暂时设置一个默认值，避免绑定错误
            byte tempCurrentSlaveId = CurrentSlaveId;

            if (value)
            {
                // 子节点启用时，从站地址下拉框显示搜索到的从站地址
                SlaveAddressOptions.Clear();
                for (byte i = 1; i < 5; i++)
                {
                    SlaveAddressOptions.Add(i);
                }
            }
            else
            {
                SlaveAddressOptions.Clear();
                SlaveAddressOptions.Add(1);
            }

            // 确保当前选择的从站地址在选项列表中
            if (SlaveAddressOptions.Contains(tempCurrentSlaveId))
            {
                CurrentSlaveId = tempCurrentSlaveId;
            }
            else if (SlaveAddressOptions.Count > 0)
            {
                CurrentSlaveId = SlaveAddressOptions[0];
            }
            else
            {
                CurrentSlaveId = 1; // 默认值
            }

            // 同步状态到串口设置视图模型（避免循环调用）
            _serialSettingsViewModel.SyncMasterNodeEnabled(value);
        }

        /// <summary>
        /// 处理从站搜索完成事件
        /// </summary>
        /// <param name="foundSlaves">搜索到的从站地址列表</param>
        private void OnSlaveSearchCompleted(List<byte> foundSlaves)
        {
            // 更新搜索到的从站地址
            FoundSlaveAddresses.Clear();
            foreach (var slave in foundSlaves)
            {
                FoundSlaveAddresses.Add(slave);
            }

            // 如果子节点已启用，更新从站地址下拉框
            if (IsMasterNodeEnabled)
            {
                SlaveAddressOptions.Clear();
                foreach (var address in FoundSlaveAddresses)
                {
                    SlaveAddressOptions.Add(address);
                }

                // 如果有搜索到的从站，自动选择第一个
                if (FoundSlaveAddresses.Count > 0)
                {
                    CurrentSlaveId = FoundSlaveAddresses[0];
                }
            }
        }

        /// <summary>
        /// 处理串口设置中主节点状态变更事件
        /// </summary>
        /// <param name="isEnabled">主节点是否启用</param>
        private void OnSerialSettingsMasterNodeEnabledChanged(bool isEnabled)
        {
            IsMasterNodeEnabled = isEnabled;
        }

        /// <summary>
        /// 处理握手完成事件，自动下载参数
        /// </summary>
        private async void OnHandshakeCompleted()
        {
            try
            {
                // 自动下载参数
                await DownloadParameters();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"自动下载参数失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 处理参数修改状态变更事件
        /// </summary>
        /// <param name="hasModified">是否有参数被修改</param>
        private void OnParameterModificationChanged(bool hasModified)
        {
            HasModifiedParameters = hasModified;
        }
    }
}